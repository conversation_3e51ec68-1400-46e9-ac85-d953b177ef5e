from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from pydantic import BaseModel, Field, HttpUrl, EmailStr
from firebase_admin import auth as firebase_auth
from firebase.config import initialize_firebase, get_db
from google.cloud import firestore
from slugify import slugify
from datetime import datetime, timezone
from auth.deps import optional_verify_token, verify_firebase_token  # Import verify_firebase_token
import httpx
import os
import resend
import logging

# Import configuration manager
try:
    from config.config_manager import get_config
    config = get_config()
    logger = logging.getLogger(__name__)
    logger.info(f"Using configuration for environment: {config.environment}")
except ImportError:
    # Fallback for backward compatibility
    config = None
    logger = logging.getLogger(__name__)
    logger.warning("Configuration manager not available, using environment variables")

# Initialize Resend with API key from configuration or environment variable
if config and hasattr(config, 'external_services'):
    resend_config = config.external_services.get('resend', {})
    resend.api_key = resend_config.get('api_key')
else:
    resend.api_key = os.environ.get("RESEND_API_KEY")

if not resend.api_key:
    logger.warning("RESEND_API_KEY not configured, welcome emails will not be sent")

router = APIRouter()
initialize_firebase()
db = get_db()

ALLOWED_ROLES = {"agent", "brand", "user"}

# Helper function to add a user to an audience
async def add_user_to_audience(user_id: str, audience_name: str):
    """
    Add a user to a Resend audience

    This function sends a webhook to add a user to an audience.
    It doesn't rely on database lookups for audience information.
    """
    try:
        # Get the appropriate URL based on environment
        if config:
            base_url = config.api_base_url
        else:
            ENV = os.getenv("ENV", "development")
            base_url = os.getenv("PROD_SITE_URL", "https://api.useadmesh.com") if ENV == "production" else os.getenv("SITE_URL", "http://127.0.0.1:8000")

        # Call the email webhook endpoint
        async with httpx.AsyncClient() as client:
            await client.post(
                f"{base_url}/email/webhook/user-signup",
                params={
                    "user_id": user_id,
                    "audience_name": audience_name
                }
            )
    except Exception as e:
        # Just log the error to console but don't store in database
        # This ensures registration continues even if audience operations fail
        logger.error(f"Error adding user to audience: {str(e)}")

# Helper function to send welcome email
async def send_welcome_email(email: str, first_name: str = None):
    """
    Send a welcome email to a newly registered user

    Args:
        email (str): The user's email address
        first_name (str, optional): The user's first name
    """
    try:
        if not resend.api_key:
            logger.warning("Skipping welcome email - RESEND_API_KEY not set")
            return

        # Read the welcome email template
        template_path = os.path.join(os.path.dirname(__file__), '..', 'templates', 'email_welcome.html')
        with open(template_path, 'r') as file:
            html_content = file.read()

        # Replace the first_name placeholder if provided
        if first_name:
            html_content = html_content.replace('{{ first_name | default("there") }}', first_name)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Welcome to AdMesh!",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Welcome email sent to {email}")
        return response

    except Exception as e:
        logger.error(f"Error sending welcome email: {str(e)}")
        # Don't raise the exception - we don't want to fail registration if email fails

# ------------------------
# Optional Product Schema
# ------------------------
class ProductModel(BaseModel):
    title: str
    url: HttpUrl
    category: str | None = None
    audience: str | None = None
    keywords: list[str] = []
    has_free_tier: bool = False
    is_open_source: bool = False
    is_ai_powered: bool = False
    confidence_score: float = 0.75
    pricing_url: HttpUrl | None = None
    audience_segment: str | None = None
    integration_list: list[str] = []

# ------------------------
# Registration Payload
# ------------------------
class RegisterPayload(BaseModel):
    email: str
    password: str
    role: str  # "agent", "brand", "user"
    name: str | None = None  # Required for agent/brand
    product: ProductModel | None = None  # Optional product on brand registration

# ------------------------
# Main Route
# ------------------------
@router.post("/auth/email-register")
async def register_user(
    payload: RegisterPayload,
    background_tasks: BackgroundTasks,
    decoded_token: dict | None = Depends(optional_verify_token)
):
    try:
        # Optionally use decoded_token if needed
        # Example: Check if the user is already authenticated
        if decoded_token:
            raise HTTPException(status_code=400, detail="User is already authenticated.")

        role = payload.role.lower().strip()
        if role not in ALLOWED_ROLES:
            raise HTTPException(status_code=400, detail="Invalid role. Must be one of: agent, brand, user")

        # 👤 Create Firebase user
        user_record = firebase_auth.create_user(email=payload.email, password=payload.password)
        uid = user_record.uid

        # 🎫 Set custom role and onboarding status for brands and agents
        custom_claims = {"role": role}
        if role == "brand":
            custom_claims["onboarding_status"] = "brand"
        elif role == "agent":
            custom_claims["onboarding_status"] = "pending"

        firebase_auth.set_custom_user_claims(uid, custom_claims)

        base_profile = {
            "uid": uid,
            "email": payload.email,
            "role": role,
            "created_at": firestore.SERVER_TIMESTAMP
        }

        # 👥 User document
        if role == "user":
            db.collection("users").document(uid).set({
                **base_profile,
                "name": payload.name,
                "clicks_made": 0,
                "conversions": 0,
                "referrer_agent": None,
                "keywords": [],
                "platform": None,
                "location": None,
                "credits": 500,
                "onboardingStatus": "pending",  # New field for user onboarding
                "seenPioneerWelcome": False,    # New field for pioneer welcome
                "xp": 0,
                "trust_score": 100
            })

            # Add user to Resend audience and send welcome email (in background)
            try:
                # Add user to audience
                background_tasks.add_task(add_user_to_audience, uid, "Registered Users")

                # Send welcome email
                if config:
                    base_url = config.api_base_url
                else:
                    ENV = os.getenv("ENV", "development")
                    base_url = os.getenv("PROD_SITE_URL", "https://api.useadmesh.com") if ENV == "production" else os.getenv("SITE_URL", "http://127.0.0.1:8000")

                async with httpx.AsyncClient() as client:
                    await client.post(
                        f"{base_url}/email/send-welcome",
                        params={
                            "email": payload.email,
                            "first_name": payload.name
                        }
                    )
                logger.info(f"Welcome email requested for {payload.email}")
            except Exception as e:
                # Just log the error but don't fail registration
                logger.error(f"Error setting up audience or sending welcome email: {str(e)}")

        # 🤖 Agent document (name is required)
        elif role == "agent":
            if not payload.name:
                raise HTTPException(status_code=422, detail="Agent must provide a name.")
            db.collection("agents").document(uid).set({
                **base_profile,
                "name": payload.name,
                "tier": "free",
                "clicks": 0,
                "conversions": 0,
                "total_earnings": 0.0,
                "referrals": 0,
                "joined_at": firestore.SERVER_TIMESTAMP,
                "trust_score": 100.0,
                "keywords": [],
                "payout_account": None,
                "onboarding_status": "pending"
            })

        # 🏢 Brand document (name = company_name required)
        elif role == "brand":
            if not payload.name:
                raise HTTPException(status_code=422, detail="Brand must provide a company name.")
            db.collection("brands").document(uid).set({
                **base_profile,
                "company_name": payload.name,
                "offers_submitted": 0,
                "budget_used": 0.0,
                "budget_remaining": 0.0,
                "verified": False,
                "domain": None,
                "logo_url": None,
                "industry": None,
                "billing_country": None,
                "payment_method": None,
                "account_manager": None,
                "onboarding_status": "brand",
                "trust_score": 100,
                "onboarding_steps": {
                    "brand": False,
                    "product": False,
                    "offer": False,
                    "tracking": False
                }
            })

            # ✅ Optional: create a product for the brand
            if payload.product:
                product = payload.product
                product_id = slugify(product.title)
                product_doc = {
                    **product.model_dump(),
                    "url": str(product.url),
                    "created_by_brand": uid,
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "last_viewed_at": datetime.now(timezone.utc).isoformat(),
                    "view_count": 0,
                    "clicks": 0,
                    "conversions": 0
                }
                db.collection("products").document(product_id).set(product_doc)

        return {
            "status": "success",
            "uid": uid,
            "email": payload.email,
            "role": role,
            "message": "Registered successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Registration failed: {str(e)}")


@router.post("/auth/debug-token")
async def debug_token(request: Request):
    """Debug endpoint to check token format"""
    try:
        logger.info("=== DEBUG TOKEN ENDPOINT ===")
        logger.info(f"Headers: {dict(request.headers)}")

        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        logger.info(f"Authorization header: {auth_header}")

        # Check request body
        body = await request.json()
        logger.info(f"Request body keys: {list(body.keys())}")

        # Check for token in body
        id_token = body.get("idToken") or body.get("id_token") or body.get("token")
        logger.info(f"Token in body: {bool(id_token)}")
        if id_token:
            logger.info(f"Token length: {len(id_token)}")

        return {
            "status": "debug",
            "auth_header_present": bool(auth_header),
            "token_in_body": bool(id_token),
            "body_keys": list(body.keys())
        }
    except Exception as e:
        logger.error(f"Debug endpoint error: {str(e)}")
        return {"error": str(e)}

@router.post("/auth/google-onboard")
async def onboard_google_user(
    request: Request,
    background_tasks: BackgroundTasks,
    decoded_token: dict = Depends(optional_verify_token)
):
    try:
        # Log the incoming request for debugging
        logger.info(f"Google onboard request received")
        logger.info(f"Headers: {dict(request.headers)}")

        # ✅ Extract role from request body first
        body = await request.json()
        role = body.get("role", "user")

        if not decoded_token:
            logger.error("No valid Firebase token found in request")
            # Check if this is a request with token in the body instead of headers
            id_token = body.get("idToken") or body.get("id_token") or body.get("token")

            if id_token:
                try:
                    logger.info("Attempting to verify token from request body")
                    decoded_token = firebase_auth.verify_id_token(id_token)
                    logger.info(f"Token from body verified successfully for user: {decoded_token.get('uid')}")
                except Exception as e:
                    logger.error(f"Token from body verification failed: {str(e)}")
                    raise HTTPException(status_code=401, detail=f"Invalid Firebase ID token: {str(e)}")
            else:
                raise HTTPException(status_code=401, detail="Missing Firebase ID token in headers or body")

        uid = decoded_token["uid"]
        email = decoded_token.get("email")
        name = decoded_token.get("name", None)

        if not email:
            raise HTTPException(status_code=400, detail="Email not found in token")

        # 🔍 First check Firebase Auth to see if this user already exists with a role
        try:
            # Get the user record from Firebase Auth
            user_record = firebase_auth.get_user(uid)

            # Check if user already has custom claims with a role
            if user_record.custom_claims and "role" in user_record.custom_claims:
                existing_role = user_record.custom_claims.get("role")

                # User already exists in Firebase Auth with a role, so they're already onboarded
                return {
                    "status": "exists",
                    "message": "User already onboarded",
                    "uid": uid,
                    "email": email,
                    "role": existing_role
                }
        except Exception as e:
            # Log the error but continue with onboarding
            logger.error(f"Error checking Firebase Auth: {str(e)}")

        # If we get here, either:
        # 1. User doesn't have custom claims with a role yet
        # 2. There was an error checking Firebase Auth

        # As a fallback, check if user already exists in the appropriate collection based on role
        if role == "user":
            collection_ref = db.collection("users").document(uid)
        elif role == "agent":
            collection_ref = db.collection("agents").document(uid)
        elif role == "brand":
            collection_ref = db.collection("brands").document(uid)
        else:
            # Default to users collection for unknown roles
            collection_ref = db.collection("users").document(uid)

        if collection_ref.get().exists:
            return {
                "status": "exists",
                "message": f"{role.capitalize()} already onboarded",
                "uid": uid,
                "email": email,
                "role": role
            }

        # ✅ Create new user document
        user_data = {
            "uid": uid,
            "email": email,
            "name": name,
            "role": role,
            "created_at": firestore.SERVER_TIMESTAMP,
            "clicks_made": 0,
            "conversions": 0,
            "referrer_agent": None,
            "keywords": [],
            "platform": None,
            "location": None,
            "credits": 500
        }

        # Add onboarding fields for users
        if role == "user":
            user_data.update({
                "onboardingStatus": "pending",  # New field for user onboarding
                "seenPioneerWelcome": False,    # New field for pioneer welcome
                "xp": 0,                        # New field for experience points
                "trust_score": 100              # Trust score for users
            })

            # Add user to Resend audience and send welcome email (in background)
            try:
                # Add user to audience
                background_tasks.add_task(add_user_to_audience, uid, "Registered Users")

                # Send welcome email
                if config:
                    base_url = config.api_base_url
                else:
                    ENV = os.getenv("ENV", "development")
                    base_url = os.getenv("PROD_SITE_URL", "https://api.useadmesh.com") if ENV == "production" else os.getenv("SITE_URL", "http://127.0.0.1:8000")

                async with httpx.AsyncClient() as client:
                    await client.post(
                        f"{base_url}/email/send-welcome",
                        params={
                            "email": email,
                            "first_name": name
                        }
                    )
                logger.info(f"Welcome email requested for {email}")
            except Exception as e:
                # Just log the error but don't fail registration
                logger.error(f"Error setting up audience or sending welcome email: {str(e)}")

        # Add trust_score for agents
        elif role == "agent":
            user_data.update({
                "trust_score": 100.0,
                "tier": "free",
                "clicks": 0,
                "conversions": 0,
                "total_earnings": 0.0,
                "referrals": 0,
                "joined_at": firestore.SERVER_TIMESTAMP,
                "payout_account": None,
                "onboarding_status": "pending"
            })

        # Set the user data in the appropriate collection
        if role == "user":
            db.collection("users").document(uid).set(user_data)
        elif role == "agent":
            db.collection("agents").document(uid).set(user_data)
        # For brands, we'll set the data later with more fields

        # 🎫 Set custom Firebase role claim and onboarding status for brands and agents
        custom_claims = {"role": role}

        # Handle brand-specific setup
        if role == "brand":
            custom_claims["onboarding_status"] = "brand"

            # Create brand document with onboarding status
            brand_data = {
                "uid": uid,
                "email": email,
                "company_name": name or "My Brand",
                "role": role,
                "created_at": firestore.SERVER_TIMESTAMP,
                "offers_submitted": 0,
                "budget_used": 0.0,
                "budget_remaining": 0.0,
                "verified": False,
                "website": None,
                "logo_url": None,
                "industry": None,
                "billing_country": None,
                "payment_method": None,
                "account_manager": None,
                "onboarding_status": "brand",
                "trust_score": 100,
                "onboarding_steps": {
                    "brand": False,
                    "product": False,
                    "offer": False,
                    "tracking": False
                }
            }
            db.collection("brands").document(uid).set(brand_data)
        elif role == "agent":
            custom_claims["onboarding_status"] = "pending"

        firebase_auth.set_custom_user_claims(uid, custom_claims)

        return {
            "status": "success",
            "message": "User successfully onboarded via Google",
            "uid": uid,
            "email": email,
            "role": role
        }

    except Exception as e:
        logger.error(f"Google onboarding failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Google onboarding failed: {str(e)}")

@router.get("/auth/check-user-exists")
def check_user_exists(email: str):
    user_ref = db.collection("users").where("email", "==", email).limit(1).stream()
    exists = any(True for _ in user_ref)
    return {"exists": exists}

class WaitlistEntry(BaseModel):
    email: EmailStr
    role: str  # "user" | "brand" | "agent"
    website: str | None = None  # Optional website for brands
    phone: str | None = None  # Optional phone number for brands

@router.post("/auth/waitlist/submit")
async def submit_waitlist(entry: WaitlistEntry, request: Request):
    email_normalized = entry.email.lower().strip()
    role = entry.role.lower()

    # Check if already exists
    existing = db.collection("waitlist").where("email", "==", email_normalized).limit(1).stream()
    for doc in existing:
        return {"status": "exists", "message": "Already on the waitlist"}, 409

    waitlist_entry = {
        "email": email_normalized,
        "role": role,
        "ip": request.client.host,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "source": "web"
    }

    # Add optional fields for brands
    if role == "brand":
        if entry.website:
            waitlist_entry["website"] = entry.website
        if entry.phone:
            waitlist_entry["phone"] = entry.phone

    db.collection("waitlist").add(waitlist_entry)
    # Send a confirmation email (optional)
    return {"status": "ok",  "message": "You're on the waitlist! We'll keep you posted with early access updates."}

class LoginResponse(BaseModel):
    status: str
    uid: str
    email: str
    role: str
    onboardingStatus: str | None = None
    seenPioneerWelcome: bool | None = None
    xp: int | None = None

@router.post("/auth/email-login")
async def email_login(decoded_token: dict = Depends(verify_firebase_token)):
    """
    Login endpoint that returns user data including onboarding status.
    This endpoint requires a valid Firebase ID token in the Authorization header.
    """
    try:
        uid = decoded_token["uid"]
        role = decoded_token.get("role")

        if not role:
            raise HTTPException(status_code=403, detail="No role assigned to user")

        # Get user document from Firestore
        user_ref = db.collection("users").document(uid)
        user_doc = user_ref.get()

        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User profile not found")

        user_data = user_doc.to_dict()

        # Prepare response with required fields
        response = {
            "status": "success",
            "uid": uid,
            "email": user_data.get("email"),
            "role": role,
        }

        # Add onboarding fields if they exist
        if role == "user":
            response["onboardingStatus"] = user_data.get("onboardingStatus", "pending")
            response["seenPioneerWelcome"] = user_data.get("seenPioneerWelcome", False)
            response["xp"] = user_data.get("xp", 0)

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")